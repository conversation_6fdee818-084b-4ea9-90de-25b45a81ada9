/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AccrualPeriodCalculation: typeof import('./src/components/participants/pensionCalculation/AccrualPeriodCalculation.vue')['default']
    AddAuthenticatorAppDialog: typeof import('./src/components/dialogs/AddAuthenticatorAppDialog.vue')['default']
    AddEditAddressDialog: typeof import('./src/components/dialogs/AddEditAddressDialog.vue')['default']
    AddEditPermissionDialog: typeof import('./src/components/dialogs/AddEditPermissionDialog.vue')['default']
    AddEditRoleDialog: typeof import('./src/components/dialogs/AddEditRoleDialog.vue')['default']
    AddParticipantForm: typeof import('./src/components/participants/AddParticipantForm.vue')['default']
    AddPaymentMethodDialog: typeof import('./src/components/dialogs/AddPaymentMethodDialog.vue')['default']
    Address: typeof import('./src/components/participants/basicInfo/Address.vue')['default']
    AppAutocomplete: typeof import('./src/@core/components/app-form-elements/AppAutocomplete.vue')['default']
    AppBarSearch: typeof import('./src/@core/components/AppBarSearch.vue')['default']
    AppCardActions: typeof import('./src/@core/components/cards/AppCardActions.vue')['default']
    AppCardCode: typeof import('./src/@core/components/cards/AppCardCode.vue')['default']
    AppCombobox: typeof import('./src/@core/components/app-form-elements/AppCombobox.vue')['default']
    AppDateTimePicker: typeof import('./src/@core/components/app-form-elements/AppDateTimePicker.vue')['default']
    AppDrawerHeaderSection: typeof import('./src/@core/components/AppDrawerHeaderSection.vue')['default']
    AppLoadingIndicator: typeof import('./src/components/AppLoadingIndicator.vue')['default']
    AppPricing: typeof import('./src/components/AppPricing.vue')['default']
    ApproveRejectCertFields: typeof import('./src/components/certified-data/ApproveRejectCertFields.vue')['default']
    ApproveRejectNewParticipantFields: typeof import('./src/components/change-proposal/requested/new-participant/ApproveRejectNewParticipantFields.vue')['default']
    AppSearchHeader: typeof import('./src/components/AppSearchHeader.vue')['default']
    AppSelect: typeof import('./src/@core/components/app-form-elements/AppSelect.vue')['default']
    AppStepper: typeof import('./src/@core/components/AppStepper.vue')['default']
    AppTextarea: typeof import('./src/@core/components/app-form-elements/AppTextarea.vue')['default']
    AppTextField: typeof import('./src/@core/components/app-form-elements/AppTextField.vue')['default']
    BasicInformation: typeof import('./src/components/participants/basicInfo/BasicInformation.vue')['default']
    BasicInformationOldTable: typeof import('./src/components/participants/basicInfo/BasicInformationOldTable.vue')['default']
    BuyNow: typeof import('./src/@core/components/BuyNow.vue')['default']
    CardAddEditDialog: typeof import('./src/components/dialogs/CardAddEditDialog.vue')['default']
    CardStatisticsHorizontal: typeof import('./src/@core/components/cards/CardStatisticsHorizontal.vue')['default']
    CardStatisticsVertical: typeof import('./src/@core/components/cards/CardStatisticsVertical.vue')['default']
    CardStatisticsVerticalSimple: typeof import('./src/@core/components/CardStatisticsVerticalSimple.vue')['default']
    CertificationActions: typeof import('./src/components/certified-data/CertificationActions.vue')['default']
    CertifiedAccrualPeriodCalculation: typeof import('./src/components/certified-data/pensionCalculation/CertifiedAccrualPeriodCalculation.vue')['default']
    CertifiedAddress: typeof import('./src/components/certified-data/basicInfo/CertifiedAddress.vue')['default']
    CertifiedBasicInformation: typeof import('./src/components/certified-data/basicInfo/CertifiedBasicInformation.vue')['default']
    CertifiedChildren: typeof import('./src/components/certified-data/basicInfo/CertifiedChildren.vue')['default']
    CertifiedDataList: typeof import('./src/components/certified-data/CertifiedDataList.vue')['default']
    CertifiedExPartners: typeof import('./src/components/certified-data/basicInfo/CertifiedExPartners.vue')['default']
    CertifiedIndexationBegining: typeof import('./src/components/certified-data/pensionCalculation/CertifiedIndexationBegining.vue')['default']
    CertifiedMaritalStatus: typeof import('./src/components/certified-data/basicInfo/CertifiedMaritalStatus.vue')['default']
    CertifiedParticipantChangeTable: typeof import('./src/components/change-proposal/requested/CertifiedParticipantChangeTable.vue')['default']
    CertifiedParticipantInformation: typeof import('./src/components/certified-data/basicInfo/CertifiedParticipantInformation.vue')['default']
    CertifiedPartTimePercentage: typeof import('./src/components/certified-data/CertifiedPartTimePercentage.vue')['default']
    CertifiedPensionBaseHeader: typeof import('./src/components/certified-data/pensionCalculation/CertifiedPensionBaseHeader.vue')['default']
    CertifiedPensionCalculationBase: typeof import('./src/components/certified-data/pensionCalculation/CertifiedPensionCalculationBase.vue')['default']
    CertifiedPensionPrimaryCalc: typeof import('./src/components/certified-data/salaryPension/CertifiedPensionPrimaryCalc.vue')['default']
    CertifiedPensionsAccrual: typeof import('./src/components/certified-data/pensionCalculation/CertifiedPensionsAccrual.vue')['default']
    CertifiedPensionsAfterCorrections: typeof import('./src/components/certified-data/pensionCalculation/CertifiedPensionsAfterCorrections.vue')['default']
    CertifiedPensionsAsPerReferenceDate: typeof import('./src/components/certified-data/pensionCalculation/CertifiedPensionsAsPerReferenceDate.vue')['default']
    CertifiedPensionsCalcStrip: typeof import('./src/components/certified-data/pensionCalculation/CertifiedPensionsCalcStrip.vue')['default']
    CertifiedPensionsEndOfPreviousCalenderYear: typeof import('./src/components/certified-data/pensionCalculation/CertifiedPensionsEndOfPreviousCalenderYear.vue')['default']
    CertifiedSalaryEntityDialog: typeof import('./src/components/certified-data/salaryPension/CertifiedSalaryEntityDialog.vue')['default']
    CertifiedSalaryEntries: typeof import('./src/components/certified-data/CertifiedSalaryEntries.vue')['default']
    CertifiedSalaryPension: typeof import('./src/components/certified-data/salaryPension/CertifiedSalaryPension.vue')['default']
    CertifiedSalaryPensionBase: typeof import('./src/components/certified-data/salaryPension/CertifiedSalaryPensionBase.vue')['default']
    ChangeFilterTest: typeof import('./src/components/change-proposal/ChangeFilterTest.vue')['default']
    Children: typeof import('./src/components/participants/basicInfo/Children.vue')['default']
    CompareChangesDialog: typeof import('./src/components/dialogs/CompareChangesDialog.vue')['default']
    CompareContents: typeof import('./src/components/dialogs/CompareContents.vue')['default']
    ConfirmDialog: typeof import('./src/components/dialogs/ConfirmDialog.vue')['default']
    CreateAppDialog: typeof import('./src/components/dialogs/CreateAppDialog.vue')['default']
    CustomCheckboxes: typeof import('./src/@core/components/app-form-elements/CustomCheckboxes.vue')['default']
    CustomCheckboxesWithIcon: typeof import('./src/@core/components/app-form-elements/CustomCheckboxesWithIcon.vue')['default']
    CustomCheckboxesWithImage: typeof import('./src/@core/components/app-form-elements/CustomCheckboxesWithImage.vue')['default']
    CustomizerSection: typeof import('./src/@core/components/CustomizerSection.vue')['default']
    CustomRadios: typeof import('./src/@core/components/app-form-elements/CustomRadios.vue')['default']
    CustomRadiosWithIcon: typeof import('./src/@core/components/app-form-elements/CustomRadiosWithIcon.vue')['default']
    CustomRadiosWithImage: typeof import('./src/@core/components/app-form-elements/CustomRadiosWithImage.vue')['default']
    DialogCloseBtn: typeof import('./src/@core/components/DialogCloseBtn.vue')['default']
    DropZone: typeof import('./src/@core/components/DropZone.vue')['default']
    EditCertificationFieldDialog: typeof import('./src/components/EditCertificationFieldDialog.vue')['default']
    EditCorrectionsDialog: typeof import('./src/components/EditCorrectionsDialog.vue')['default']
    EditFieldDialog: typeof import('./src/components/EditFieldDialog.vue')['default']
    EmploymentInfo: typeof import('./src/components/participants/basicInfo/EmploymentInfo.vue')['default']
    EnableOneTimePasswordDialog: typeof import('./src/components/dialogs/EnableOneTimePasswordDialog.vue')['default']
    ErrorHeader: typeof import('./src/components/ErrorHeader.vue')['default']
    ExPartners: typeof import('./src/components/participants/basicInfo/ExPartners.vue')['default']
    I18n: typeof import('./src/@core/components/I18n.vue')['default']
    IndexationBegining: typeof import('./src/components/participants/pensionCalculation/IndexationBegining.vue')['default']
    IndexationPensionDataUI: typeof import('./src/components/IndexationPensionDataUI.vue')['default']
    ManageUsersTable: typeof import('./src/components/Users/<USER>')['default']
    MaritalStatus: typeof import('./src/components/participants/basicInfo/MaritalStatus.vue')['default']
    MoreBtn: typeof import('./src/@core/components/MoreBtn.vue')['default']
    NewAddress: typeof import('./src/components/change-proposal/requested/new-participant/NewAddress.vue')['default']
    NewBasicInformation: typeof import('./src/components/change-proposal/requested/new-participant/NewBasicInformation.vue')['default']
    NewChildren: typeof import('./src/components/change-proposal/requested/new-participant/NewChildren.vue')['default']
    NewEmploymentInfo: typeof import('./src/components/change-proposal/requested/new-participant/NewEmploymentInfo.vue')['default']
    NewExPartners: typeof import('./src/components/change-proposal/requested/new-participant/NewExPartners.vue')['default']
    NewMaritalStatus: typeof import('./src/components/change-proposal/requested/new-participant/NewMaritalStatus.vue')['default']
    NewParticipantInformation: typeof import('./src/components/change-proposal/requested/new-participant/NewParticipantInformation.vue')['default']
    NewParticipantsTable: typeof import('./src/components/change-proposal/requested/NewParticipantsTable.vue')['default']
    NewPartTimePercentage: typeof import('./src/components/change-proposal/requested/new-participant/NewPartTimePercentage.vue')['default']
    NewPensionInfo: typeof import('./src/components/change-proposal/requested/new-participant/NewPensionInfo.vue')['default']
    NewSalaryEntries: typeof import('./src/components/change-proposal/requested/new-participant/NewSalaryEntries.vue')['default']
    NewSalaryPension: typeof import('./src/components/change-proposal/requested/new-participant/NewSalaryPension.vue')['default']
    NewSalaryPensionBase: typeof import('./src/components/change-proposal/requested/new-participant/NewSalaryPensionBase.vue')['default']
    Notifications: typeof import('./src/@core/components/Notifications.vue')['default']
    NotificationsTable: typeof import('./src/components/notifications/NotificationsTable.vue')['default']
    ParticipantChangeHistoryTable: typeof import('./src/components/change-proposal/history/ParticipantChangeHistoryTable.vue')['default']
    ParticipantChangeTable: typeof import('./src/components/change-proposal/requested/ParticipantChangeTable.vue')['default']
    ParticipantInformation: typeof import('./src/components/participants/basicInfo/ParticipantInformation.vue')['default']
    ParticipantsCertificationTable: typeof import('./src/components/participants/ParticipantsCertificationTable.vue')['default']
    ParticipantsFilter: typeof import('./src/components/participants/ParticipantsFilter.vue')['default']
    ParticipantsTable: typeof import('./src/components/participants/ParticipantsTable.vue')['default']
    PartTimePercentage: typeof import('./src/components/participants/salaryPension/PartTimePercentage.vue')['default']
    PaymentProvidersDialog: typeof import('./src/components/dialogs/PaymentProvidersDialog.vue')['default']
    PensionBaseHeader: typeof import('./src/components/participants/pensionCalculation/PensionBaseHeader.vue')['default']
    PensionCalculationBase: typeof import('./src/components/participants/pensionCalculation/PensionCalculationBase.vue')['default']
    PensionCode: typeof import('./src/components/participants/pensionInfo/PensionCode.vue')['default']
    PensionCodeDialog: typeof import('./src/components/participants/pensionInfo/PensionCodeDialog.vue')['default']
    PensionInfo: typeof import('./src/components/participants/basicInfo/PensionInfo.vue')['default']
    PensionParamChangeHistoryTable: typeof import('./src/components/change-proposal/history/PensionParamChangeHistoryTable.vue')['default']
    PensionParamChangeTable: typeof import('./src/components/change-proposal/requested/PensionParamChangeTable.vue')['default']
    PensionPrimaryCalc: typeof import('./src/components/participants/salaryPension/PensionPrimaryCalc.vue')['default']
    PensionsAccrual: typeof import('./src/components/participants/pensionCalculation/PensionsAccrual.vue')['default']
    PensionsAfterCorrections: typeof import('./src/components/participants/pensionCalculation/PensionsAfterCorrections.vue')['default']
    PensionsAsPerReferenceDate: typeof import('./src/components/participants/pensionCalculation/PensionsAsPerReferenceDate.vue')['default']
    PensionsCalcStrip: typeof import('./src/components/participants/pensionCalculation/PensionsCalcStrip.vue')['default']
    PensionsEndOfPreviousCalenderYear: typeof import('./src/components/participants/pensionCalculation/PensionsEndOfPreviousCalenderYear.vue')['default']
    PensionTableLayout: typeof import('./src/components/participants/pensionCalculation/PensionTableLayout.vue')['default']
    PensionTableRow: typeof import('./src/components/participants/pensionCalculation/PensionTableRow.vue')['default']
    PricingPlanDialog: typeof import('./src/components/dialogs/PricingPlanDialog.vue')['default']
    ProductDescriptionEditor: typeof import('./src/@core/components/ProductDescriptionEditor.vue')['default']
    ReferAndEarnDialog: typeof import('./src/components/dialogs/ReferAndEarnDialog.vue')['default']
    RejectChangeDialog: typeof import('./src/components/change-proposal/RejectChangeDialog.vue')['default']
    RejectPersonalInfoFieldDialog: typeof import('./src/components/change-proposal/RejectPersonalInfoFieldDialog.vue')['default']
    RevertApproveRejectCertFields: typeof import('./src/components/certified-data/RevertApproveRejectCertFields.vue')['default']
    RevertChangesDialog: typeof import('./src/components/dialogs/RevertChangesDialog.vue')['default']
    RevertNewParticipantFields: typeof import('./src/components/change-proposal/requested/new-participant/RevertNewParticipantFields.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SalaryEntityDialog: typeof import('./src/components/participants/salaryPension/SalaryEntityDialog.vue')['default']
    SalaryEntries: typeof import('./src/components/participants/salaryPension/SalaryEntries.vue')['default']
    SalaryPension: typeof import('./src/components/participants/salaryPension/SalaryPension.vue')['default']
    SalaryPensionBase: typeof import('./src/components/participants/salaryPension/SalaryPensionBase.vue')['default']
    ScrollToTop: typeof import('./src/@core/components/ScrollToTop.vue')['default']
    ShareProjectDialog: typeof import('./src/components/dialogs/ShareProjectDialog.vue')['default']
    Shortcuts: typeof import('./src/@core/components/Shortcuts.vue')['default']
    SingleColumnTableLayout: typeof import('./src/components/certified-data/pensionCalculation/SingleColumnTableLayout.vue')['default']
    SingleColumnTableRow: typeof import('./src/components/certified-data/pensionCalculation/SingleColumnTableRow.vue')['default']
    TablePagination: typeof import('./src/@core/components/TablePagination.vue')['default']
    TempBasicInfo: typeof import('./src/components/participants/basicInfo/tempBasicInfo.vue')['default']
    TheCustomizer: typeof import('./src/@core/components/TheCustomizer.vue')['default']
    ThemeSwitcher: typeof import('./src/@core/components/ThemeSwitcher.vue')['default']
    TiptapEditor: typeof import('./src/@core/components/TiptapEditor.vue')['default']
    TwoFactorAuthDialog: typeof import('./src/components/dialogs/TwoFactorAuthDialog.vue')['default']
    UserDialog: typeof import('./src/components/Users/<USER>')['default']
    UserInfoEditDialog: typeof import('./src/components/dialogs/UserInfoEditDialog.vue')['default']
    UserUpgradePlanDialog: typeof import('./src/components/dialogs/UserUpgradePlanDialog.vue')['default']
  }
}
