<script setup lang="ts">
  import { useParticipants } from '@/composables/participants/useParticipants'
  import { useRoute } from 'vue-router'

  const { actions:{refetchSingleParticipant} } = useParticipants()

  const route = useRoute()
  const id = computed(() => route.params.id as string)

  watch(
    () => id.value,
    () => {
      refetchSingleParticipant()
    },
    { immediate: true }
  )
</script>

<template>
  <NewBasicInformation :editorView="true" />
  <NewParticipantInformation />
  <NewEmploymentInfo />
  <NewPensionInfo />
  <NewSalaryPensionBase />
</template>

<style scoped lang="scss">
</style>