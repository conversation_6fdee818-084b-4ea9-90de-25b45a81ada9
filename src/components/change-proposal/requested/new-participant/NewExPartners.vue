<script setup lang="ts">
  import { ref, computed } from 'vue';
  import { useParticipants } from '@/composables/participants/useParticipants'
  import { useAppStore } from '@/stores/app/appStore'

  const { state: { participantExPartnerInfo } } = useParticipants()
  const appStore = useAppStore()

  const editDialog = ref(false);
  const selectedField = ref({
    field: '',
    value: '',
    id: '',
    partnerIndex: 0,
    typename: ''
  });

  const filteredPartners = computed(() => {
    return participantExPartnerInfo.value.map(partner =>
      partner.filter(item => item.field !== 'id' && item.field !== 'isCurrent')
    );
  });

  const openEditDialog = (item: any, partnerIndex: number) => {
    if (item.disabled) {
      appStore.showSnack('Sorry you cannot edit this field');
      return;
    }

    selectedField.value = {
      field: item.field,
      value: item.value,
      id: item.id,
      partnerIndex,
      typename: item.typename
    };
    editDialog.value = true;
  };

  const closeEditDialog = () => {
    editDialog.value = false;
  };

  const updateField = (newValue: any) => {
    closeEditDialog();
  };
</script>

<template>
  <div class="ex-partners-container">
    <div class="header-container">
      <h2>Ex-Partners</h2>
    </div>

    <div v-if="filteredPartners.length > 0">
      <div
        v-for="(partner, partnerIndex) in filteredPartners"
        :key="partner[0].id"
        class="partner-section"
      >
        <div class="partner-header">
          <h3>Ex-Partner {{ partnerIndex + 1 }}</h3>
        </div>

        <VDataTable
          :headers="[
            { title: 'Field', key: 'name', width: '200px' },
            { title: 'Value', key: 'value' },
            { title: 'Actions', key: 'actions', sortable: false },
          ]"
          :items="partner"
          hide-default-footer
          class="elevation-0"
          density="compact"
        >
          <template #item.actions="{ item }">
            <VBtn
              icon
              size="small"
              variant="text"
              color="primary"
              @click="openEditDialog(item, partnerIndex)"
            >
              <VIcon
                v-if="item?.disabled"
                size="16"
                icon="tabler-alert-triangle"
                class="edit-icon ms-1 ml-4"
                color="error"
              />
              <VIcon
                v-else
                size="16"
                icon="tabler-edit"
                class="edit-icon ms-1 ml-4"
                color="primary"
              />
            </VBtn>
          </template>
        </VDataTable>
      </div>
    </div>

    <div v-else class="no-partners-message">
      No ex-partners found
    </div>

    <!-- Dynamic Dialog -->
    <EditFieldDialog
      v-if="editDialog"
      v-model="editDialog"
      :field="selectedField.field"
      :currentValue="selectedField.value"
      :entity-id="selectedField.id"
      :entity-type="selectedField.typename"
      :year="2025"
      @close="closeEditDialog"
      @update="updateField"
    />
  </div>
</template>

<style scoped>
  .ex-partners-container {
    border: 1px solid #eee;
    border-radius: 8px;
    margin-bottom: 20px;
    padding: 16px;
    background-color: white;
  }

  .header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }

  .partner-section {
    margin-bottom: 16px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    overflow: hidden;
  }

  .partner-header {
    padding: 12px 16px;
    background-color: #f5f5f5;
  }

  .no-partners-message {
    padding: 16px;
    text-align: center;
    color: #666;
  }
</style>