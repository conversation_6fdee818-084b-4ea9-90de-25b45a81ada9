<script setup lang="ts">
  import { useParticipants } from '@/composables/participants/useParticipants'
  import { useAppStore } from '@/stores/app/appStore'
  import GenericRejectFieldDialog from '@/components/change-proposal/GenericRejectFieldDialog.vue'

  const props = defineProps({
    showRejectOptions: {
      type: Boolean,
      default: true
    },
    editorView: {
      type: Boolean,
      default: false
    }
  });

  const { state: { participantPartnerInfo, participantDetails, loadingParticipant }, actions: { refetchSingleParticipant } } = useParticipants()

  const emit = defineEmits(['update:maritalStatus', 'field-rejected']);

  const appStore = useAppStore()

  const editDialog = ref(false);
  const selectedField = ref({ field: '', value: '', id: '' });

  const rejectDialog = ref(false);
  const fieldToReject = ref(null);

  // Get rejected fields with reasons for editor view
  const rejectedFieldsWithReasons = computed(() => {
    if (!props.editorView) return [];

    const rejectedFieldsArray: Array<{ field: string, fieldKey: string, reason: string }> = [];

    // Find the certificationRejectReason field in participantDetails
    const certificationRejectReasonField = participantDetails.value.find((item: any) => item.field === 'certificationRejectReason');

    if (certificationRejectReasonField?.value && Array.isArray(certificationRejectReasonField.value)) {
      certificationRejectReasonField.value.forEach((item: any) => {
        // Only include partner-related fields
        if (item.reason && item.status === 'VALID' && isPartnerField(item.field)) {
          rejectedFieldsArray.push({
            field: fieldKeyToName(item.field),
            fieldKey: item.field,
            reason: item.reason
          });
        }
      });
    }

    return rejectedFieldsArray;
  });

  // Get rejected fields based on pendingChanges array (for reviewer view)
  const rejectedFields = computed(() => {
    if (!props.showRejectOptions || props.editorView) return [];

    const rejectedFieldsArray: Array<{ field: string, fieldKey: string }> = [];

    // Find personalInfo field that contains the pendingChanges
    const personalInfoField = participantDetails.value.find((item: any) => item.field === 'personalInfo');
    const personalInfoValue = personalInfoField?.value as any;
    const pendingChanges = personalInfoValue?.pendingChanges || [];

    // Convert pending changes to rejected fields display for partner-related fields
    pendingChanges.forEach((fieldKey: string) => {
      if (isPartnerField(fieldKey)) {
        rejectedFieldsArray.push({
          field: fieldKeyToName(fieldKey),
          fieldKey: fieldKey
        });
      }
    });

    return rejectedFieldsArray;
  });

  // Check if a field is partner-related
  function isPartnerField(fieldKey: string): boolean {
    const partnerFields = ['firstName', 'lastName', 'dateOfBirth', 'startDate', 'isDeceased', 'maritalStatus'];
    return partnerFields.includes(fieldKey);
  }

  function fieldKeyToName(fieldKey: string): string {
    if (!fieldKey) return '';
    const result = fieldKey.replace(/([A-Z])/g, ' $1');
    return result.charAt(0).toUpperCase() + result.slice(1).trim();
  }

  const filteredList = computed(() => {
    return participantPartnerInfo.value.filter(item => item.field !== 'id' && item.field !== 'isCurrent' && item.field !== 'startDate');
  });

  const refreshing = ref(false);

  const openEditDialog = (item: any) => {
    if (item.disabled) {
      appStore.showSnack('Sorry you cannot edit this field');
      return
    };
    selectedField.value = item;
    editDialog.value = true;
  };

  const closeEditDialog = () => {
    editDialog.value = false;
  };

  const openRejectDialog = (item: any) => {
    if (!props.showRejectOptions || refreshing.value || props.editorView) {
      return;
    }

    fieldToReject.value = item;
    rejectDialog.value = true;
  };

  const closeRejectDialog = () => {
    rejectDialog.value = false;
    fieldToReject.value = null;
  };

  const handleFieldRejected = async (rejectionData: any) => {
    closeRejectDialog();
    emit('field-rejected', rejectionData);

    // Set refreshing state and show loading feedback
    refreshing.value = true;
    appStore.showSnack('Updating field status...');

    // Refresh participant data to get updated values with rejection information
    try {
      await refetchSingleParticipant();
      appStore.showSnack(`Field "${rejectionData.field}" rejected and marked as pending`);
    } catch (error) {
      console.error('Error refreshing participant data:', error);
      appStore.showSnack('Error updating field status');
    } finally {
      refreshing.value = false;
    }
  };

  const getFieldValue = (fieldName: string) => {
    const field = participantDetails.value.find((item: any) => item.field === fieldName);
    return field ? field.value : '';
  };

  const fullName = computed(() => {
    const firstName = getFieldValue('firstName');
    const lastName = getFieldValue('lastName');
    return `${firstName} ${lastName}`;
  });

  const updateField = () => {
    // Handle field update - will be implemented based on specific requirements
    // This is called when the edit dialog confirms an update
    closeEditDialog();
  };

</script>

<template>
  <div class="marital-status-container">
    <div class="header-container">
      <h2>Marital Status</h2>
    </div>

    <!-- Alerts for rejected fields with reasons (Editor View) -->
    <div v-if="rejectedFieldsWithReasons.length > 0 && editorView" class="mb-4">
      <VAlert
        v-for="(item, index) in rejectedFieldsWithReasons"
        :key="`rejected-reason-${item.fieldKey}-${index}`"
        type="error"
        variant="tonal"
        class="mb-2"
        closable
      >
        <template #prepend>
          <VIcon
            icon="tabler-x"
            size="10"
          />
        </template>
        <div class="d-flex align-center">
          <strong class="mr-2">{{ item.field }}:</strong>
          <span>{{ item.reason }}</span>
          <VChip
            size="small"
            color="error"
            class="ml-auto"
          >
            Needs Fix
          </VChip>
        </div>
      </VAlert>
    </div>

    <!-- Simple alerts for rejected fields (Reviewer View) -->
    <div v-if="rejectedFields.length > 0 && !editorView" class="mb-4">
      <VAlert
        v-for="(item, index) in rejectedFields"
        :key="`rejected-${item.fieldKey}-${index}`"
        type="info"
        variant="tonal"
        class="mb-2"
        closable
      >
        <template #prepend>
          <VIcon
            icon="tabler-info-circle"
            size="10"
          />
        </template>
        <div class="d-flex align-center">
          <strong class="mr-2">{{ item.field }}:</strong>
          <span>Field has been rejected and is pending review</span>
          <VChip
            size="small"
            color="primary"
            class="ml-auto"
          >
            Follow-up submitted
          </VChip>
        </div>
      </VAlert>
    </div>

    <div v-if="filteredList.length > 0">
      <VDataTable
        :headers="[
          { title: 'Field', key: 'name' },
          { title: 'Value', key: 'value' },
          { title: 'Actions', key: 'actions', sortable: false },
        ]"
        :items="filteredList"
        hide-default-footer
        class="elevation-0"
        density="compact"
      >
        <!-- Custom row styling for disabled fields -->
        <template #item.name="{ item }">
          <span :class="{ 'dimmed-text': item.disabled }">{{ item.name }}</span>
        </template>

        <template #item.value="{ item }">
          <template v-if="item.disabled">
            <VChip
              size="small"
              color="error"
              variant="tonal"
              class="font-weight-medium"
            >
              {{ item.value }}
              <v-tooltip activator="parent" location="top">
                This field was rejected and is pending review
              </v-tooltip>
            </VChip>
          </template>
          <template v-else>
            <span :class="{ 'dimmed-text': item.disabled }">{{ item.value }}</span>
          </template>
        </template>

        <template #item.actions="{ item }">
          <div class="d-flex gap-1">
            <!-- Edit Button -->
            <VBtn
              icon
              size="small"
              variant="text"
              color="primary"
              @click="openEditDialog(item)"
            >
              <VIcon
                v-if="item?.disabled"
                size="16"
                icon="tabler-alert-triangle"
                class="edit-icon"
                color="error"
              />
              <VIcon
                v-else
                size="16"
                icon="tabler-edit"
                class="edit-icon"
                color="primary"
              />
              <v-tooltip activator="parent" location="top">
                {{ item.disabled ? 'Field rejected' : 'Edit field' }}
              </v-tooltip>
            </VBtn>

            <!-- Reject Button (only show for reviewer view and non-disabled fields) -->
            <template v-if="showRejectOptions && !editorView && !item.disabled && !refreshing">
              <VBtn
                icon
                size="small"
                variant="text"
                color="error"
                @click="openRejectDialog(item)"
              >
                <VIcon
                  size="16"
                  icon="tabler-x"
                  class="reject-icon"
                />
                <v-tooltip activator="parent" location="top">
                  Reject this field
                </v-tooltip>
              </VBtn>
            </template>

            <!-- Show loading spinner while refreshing (reviewer view only) -->
            <template v-else-if="showRejectOptions && !editorView && !item.disabled && refreshing">
              <VBtn
                icon
                size="small"
                variant="text"
                disabled
              >
                <VIcon
                  size="16"
                  icon="tabler-loader-2"
                  class="spinning"
                  color="primary"
                />
                <v-tooltip activator="parent" location="top">
                  Updating field status...
                </v-tooltip>
              </VBtn>
            </template>
          </div>
        </template>
      </VDataTable>
    </div>

    <div v-else class="text-caption text-disabled pa-4 text-center">
      No partner information available.
    </div>

    <!-- Dynamic Dialog -->
    <EditFieldDialog
      v-if="editDialog"
      v-model="editDialog"
      :field="selectedField.field"
      :currentValue="selectedField.value"
      :entity-id="selectedField.id"
      :entity-type="participantPartnerInfo[0]?.typename"
      :year="2025"
      @close="closeEditDialog"
      @update="updateField"
    />

    <!-- Reject Dialog (only for reviewer view) -->
    <GenericRejectFieldDialog
      v-if="rejectDialog && fieldToReject && !editorView"
      v-model="rejectDialog"
      :field="fieldToReject"
      :participant-name="fullName"
      :user-id="currentUserId"
      @close="closeRejectDialog"
      @rejected="handleFieldRejected"
    />
  </div>
</template>

<style scoped>
  .marital-status-container {
    border: 1px solid #eee;
    border-radius: 8px;
    margin-bottom: 20px;
    padding: 16px;
    background-color: white;
  }

  .header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }

  .marital-status-section {
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    overflow: hidden;
  }

  .dimmed-text {
    opacity: 0.6;
    color: #6c757d !important;
  }

  .reject-icon {
    opacity: 0.7;
    transition: opacity 0.2s;
  }

  .reject-icon:hover {
    opacity: 1;
  }

  .edit-icon {
    opacity: 0.7;
    transition: opacity 0.2s;
  }

  .edit-icon:hover {
    opacity: 1;
  }

  .spinning {
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
</style>